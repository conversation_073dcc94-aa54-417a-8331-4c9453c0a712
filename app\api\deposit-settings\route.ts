import { NextRequest, NextResponse } from 'next/server';
import { getDepositSettings } from '@/lib/deposit-settings';

// GET /api/deposit-settings - Get public deposit settings for frontend
export async function GET(request: NextRequest) {
  try {
    const settings = await getDepositSettings();
    
    // Only return public settings (don't expose internal configuration)
    return NextResponse.json({
      success: true,
      depositPercentage: settings.depositPercentage,
      isDepositEnabled: settings.isDepositEnabled,
    });
  } catch (error) {
    console.error('Error fetching public deposit settings:', error);
    return NextResponse.json(
      { error: 'Failed to fetch deposit settings' },
      { status: 500 }
    );
  }
}
