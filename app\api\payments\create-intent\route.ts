import { calculateDepositAmount, getDepositSettings } from "@/lib/deposit-settings";
import { createPaymentIntent, eurosToCents } from "@/lib/stripe";
import { supabase, supabaseAdmin } from "@/lib/supabase";
import { NextRequest, NextResponse } from "next/server";

export async function POST(request: NextRequest) {
	try {
		const { reservationId, paymentType = "full", amount } = await request.json();

		console.log("=== PAYMENT INTENT CREATION DEBUG ===");
		console.log("Request body:", { reservationId, paymentType, amount });

		if (!reservationId) {
			console.log("ERROR: No reservation ID provided");
			return NextResponse.json({ error: "Reservation ID is required" }, { status: 400 });
		}

		console.log("Looking up reservation with ID:", reservationId);

		// Use admin client for server-side operations
		const adminClient = supabaseAdmin || supabase;

		// Fetch reservation details
		const { data: reservation, error: reservationError } = await adminClient
			.from("reservations")
			.select(
				`
        *,
        customer:customers(first_name, last_name, email),
        service:services(name)
      `
			)
			.eq("id", reservationId)
			.single();

		console.log("Reservation query result:", { reservation, reservationError });

		if (reservationError || !reservation) {
			console.log("ERROR: Reservation not found or error occurred:", reservationError);
			return NextResponse.json({ error: "Reservation not found" }, { status: 404 });
		}

		// Check if reservation already has a successful payment
		const { data: existingPayment } = await adminClient
			.from("payments")
			.select("*")
			.eq("reservation_id", reservationId)
			.eq("status", "succeeded")
			.single();

		if (existingPayment) {
			return NextResponse.json({ error: "Reservation already paid" }, { status: 400 });
		}

		// Calculate payment amount based on payment type
		let paymentAmount = amount || reservation.total_amount;
		let depositPercentage = null;

		if (paymentType === "deposit") {
			const depositSettings = await getDepositSettings();
			paymentAmount = calculateDepositAmount(reservation.total_amount, depositSettings);
			depositPercentage = depositSettings.depositPercentage;
		}

		// Create payment intent
		const amountInCents = eurosToCents(paymentAmount);
		const customerName = reservation.customer
			? `${reservation.customer.first_name} ${reservation.customer.last_name}`
			: "Unknown Customer";

		const paymentTypeLabel = paymentType === "deposit" ? `Deposit (${depositPercentage}%)` : "Full Payment";
		const result = await createPaymentIntent({
			amount: amountInCents,
			currency: "eur",
			reservationId,
			customerEmail: reservation.customer?.email,
			customerName,
			description: `${paymentTypeLabel} for ${reservation.service?.name || "service"} - Reservation #${
				reservation.reservation_number
			}`,
		});

		if (!result.success) {
			return NextResponse.json({ error: result.error || "Failed to create payment intent" }, { status: 500 });
		}

		// Store payment record in database
		const { error: paymentError } = await adminClient.from("payments").insert({
			reservation_id: reservationId,
			payment_intent_id: result.paymentIntent!.id,
			amount: paymentAmount,
			currency: "EUR",
			status: "pending",
			payment_method: "stripe",
			payment_type: paymentType,
			deposit_percentage: depositPercentage,
			is_deposit: paymentType === "deposit",
		});

		if (paymentError) {
			console.error("Error storing payment record:", paymentError);
			// Don't fail the request, as the payment intent was created successfully
		}

		return NextResponse.json({
			success: true,
			clientSecret: result.clientSecret,
			paymentIntentId: result.paymentIntent!.id,
			amount: paymentAmount,
			currency: "EUR",
			paymentType: paymentType,
			depositPercentage: depositPercentage,
			totalAmount: reservation.total_amount,
		});
	} catch (error) {
		console.error("Error creating payment intent:", error);
		return NextResponse.json({ error: "Internal server error" }, { status: 500 });
	}
}
