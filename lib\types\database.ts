export type Json = string | number | boolean | null | { [key: string]: Json | undefined } | Json[];

export interface Database {
	public: {
		Tables: {
			admin_audit_log: {
				Row: {
					id: string;
					admin_user_id: string | null;
					action: string;
					table_name: string | null;
					record_id: string | null;
					old_values: Json | null;
					new_values: Json | null;
					ip_address: string | null;
					user_agent: string | null;
					session_id: string | null;
					created_at: string | null;
				};
				Insert: {
					id?: string;
					admin_user_id?: string | null;
					action: string;
					table_name?: string | null;
					record_id?: string | null;
					old_values?: Json | null;
					new_values?: Json | null;
					ip_address?: string | null;
					user_agent?: string | null;
					session_id?: string | null;
					created_at?: string | null;
				};
				Update: {
					id?: string;
					admin_user_id?: string | null;
					action?: string;
					table_name?: string | null;
					record_id?: string | null;
					old_values?: Json | null;
					new_values?: Json | null;
					ip_address?: string | null;
					user_agent?: string | null;
					session_id?: string | null;
					created_at?: string | null;
				};
			};
			customer_feedback: {
				Row: {
					id: string;
					reservation_id: string;
					customer_id: string;
					rating: number;
					review_text: string | null;
					service_quality_rating: number | null;
					staff_rating: number | null;
					equipment_rating: number | null;
					would_recommend: boolean | null;
					is_public: boolean | null;
					response_text: string | null;
					responded_by: string | null;
					responded_at: string | null;
					created_at: string | null;
					updated_at: string | null;
				};
				Insert: {
					id?: string;
					reservation_id: string;
					customer_id: string;
					rating: number;
					review_text?: string | null;
					service_quality_rating?: number | null;
					staff_rating?: number | null;
					equipment_rating?: number | null;
					would_recommend?: boolean | null;
					is_public?: boolean | null;
					response_text?: string | null;
					responded_by?: string | null;
					responded_at?: string | null;
					created_at?: string | null;
					updated_at?: string | null;
				};
				Update: {
					id?: string;
					reservation_id?: string;
					customer_id?: string;
					rating?: number;
					review_text?: string | null;
					service_quality_rating?: number | null;
					staff_rating?: number | null;
					equipment_rating?: number | null;
					would_recommend?: boolean | null;
					is_public?: boolean | null;
					response_text?: string | null;
					responded_by?: string | null;
					responded_at?: string | null;
					created_at?: string | null;
					updated_at?: string | null;
				};
			};
			customers: {
				Row: {
					id: string;
					email: string;
					first_name: string;
					last_name: string;
					phone: string | null;
					date_of_birth: string | null;
					nationality: string | null;
					emergency_contact_name: string | null;
					emergency_contact_phone: string | null;
					dietary_restrictions: string | null;
					medical_conditions: string | null;
					marketing_consent: boolean | null;
					created_at: string | null;
					updated_at: string | null;
				};
				Insert: {
					id: string;
					email: string;
					first_name: string;
					last_name: string;
					phone?: string | null;
					date_of_birth?: string | null;
					nationality?: string | null;
					emergency_contact_name?: string | null;
					emergency_contact_phone?: string | null;
					dietary_restrictions?: string | null;
					medical_conditions?: string | null;
					marketing_consent?: boolean | null;
					created_at?: string | null;
					updated_at?: string | null;
				};
				Update: {
					id?: string;
					email?: string;
					first_name?: string;
					last_name?: string;
					phone?: string | null;
					date_of_birth?: string | null;
					nationality?: string | null;
					emergency_contact_name?: string | null;
					emergency_contact_phone?: string | null;
					dietary_restrictions?: string | null;
					medical_conditions?: string | null;
					marketing_consent?: boolean | null;
					created_at?: string | null;
					updated_at?: string | null;
				};
			};
			discount_coupons: {
				Row: {
					id: string;
					code: string;
					description: string | null;
					discount_type: string;
					discount_value: number;
					min_purchase_amount: number | null;
					max_discount_amount: number | null;
					usage_limit: number | null;
					current_usage: number | null;
					valid_from: string;
					valid_until: string;
					applicable_services: string[] | null;
					is_active: boolean | null;
					created_by: string | null;
					created_at: string | null;
					updated_at: string | null;
				};
				Insert: {
					id?: string;
					code: string;
					description?: string | null;
					discount_type: string;
					discount_value: number;
					min_purchase_amount?: number | null;
					max_discount_amount?: number | null;
					usage_limit?: number | null;
					current_usage?: number | null;
					valid_from: string;
					valid_until: string;
					applicable_services?: string[] | null;
					is_active?: boolean | null;
					created_by?: string | null;
					created_at?: string | null;
					updated_at?: string | null;
				};
				Update: {
					id?: string;
					code?: string;
					description?: string | null;
					discount_type?: string;
					discount_value?: number;
					min_purchase_amount?: number | null;
					max_discount_amount?: number | null;
					usage_limit?: number | null;
					current_usage?: number | null;
					valid_from?: string;
					valid_until?: string;
					applicable_services?: string[] | null;
					is_active?: boolean | null;
					created_by?: string | null;
					created_at?: string | null;
					updated_at?: string | null;
				};
			};
			profiles: {
				Row: {
					id: string;
					email: string;
					first_name: string | null;
					last_name: string | null;
					phone: string | null;
					role: "admin" | "employee" | "customer";
					created_at: string;
					updated_at: string;
				};
				Insert: {
					id: string;
					email: string;
					first_name?: string | null;
					last_name?: string | null;
					phone?: string | null;
					role?: "admin" | "employee" | "customer";
					created_at?: string;
					updated_at?: string;
				};
				Update: {
					id?: string;
					email?: string;
					first_name?: string | null;
					last_name?: string | null;
					phone?: string | null;
					role?: "admin" | "employee" | "customer";
					created_at?: string;
					updated_at?: string;
				};
			};
			services: {
				Row: {
					id: string;
					name: string;
					description: string | null;
					duration_minutes: number;
					buffer_time_minutes: number;
					base_price: number;
					max_participants: number;
					min_age: number;
					max_age: number | null;
					is_family_friendly: boolean;
					is_active: boolean;
					image_url: string | null;
					default_employee_id: string | null;
					requires_qualification: boolean;
					auto_assign_employees: boolean;
					created_at: string;
					updated_at: string;
				};
				Insert: {
					id?: string;
					name: string;
					description?: string | null;
					duration_minutes: number;
					buffer_time_minutes?: number;
					base_price: number;
					max_participants: number;
					min_age?: number;
					max_age?: number | null;
					is_family_friendly?: boolean;
					is_active?: boolean;
					image_url?: string | null;
					default_employee_id?: string | null;
					requires_qualification?: boolean;
					auto_assign_employees?: boolean;
					created_at?: string;
					updated_at?: string;
				};
				Update: {
					id?: string;
					name?: string;
					description?: string | null;
					duration_minutes?: number;
					buffer_time_minutes?: number;
					base_price?: number;
					max_participants?: number;
					min_age?: number;
					max_age?: number | null;
					is_family_friendly?: boolean;
					is_active?: boolean;
					image_url?: string | null;
					default_employee_id?: string | null;
					requires_qualification?: boolean;
					auto_assign_employees?: boolean;
					created_at?: string;
					updated_at?: string;
				};
			};
			employee_availability: {
				Row: {
					id: string;
					employee_id: string | null;
					day_of_week: number;
					start_time: string;
					end_time: string;
					is_available: boolean | null;
					effective_from: string | null;
					effective_until: string | null;
					created_at: string | null;
					updated_at: string | null;
				};
				Insert: {
					id?: string;
					employee_id?: string | null;
					day_of_week: number;
					start_time: string;
					end_time: string;
					is_available?: boolean | null;
					effective_from?: string | null;
					effective_until?: string | null;
					created_at?: string | null;
					updated_at?: string | null;
				};
				Update: {
					id?: string;
					employee_id?: string | null;
					day_of_week?: number;
					start_time?: string;
					end_time?: string;
					is_available?: boolean | null;
					effective_from?: string | null;
					effective_until?: string | null;
					created_at?: string | null;
					updated_at?: string | null;
				};
			};
			employee_service_qualifications: {
				Row: {
					id: string;
					employee_id: string | null;
					service_id: string | null;
					qualification_level: string | null;
					certified_date: string | null;
					expiry_date: string | null;
					notes: string | null;
					is_active: boolean | null;
					created_at: string | null;
					updated_at: string | null;
				};
				Insert: {
					id?: string;
					employee_id?: string | null;
					service_id?: string | null;
					qualification_level?: string | null;
					certified_date?: string | null;
					expiry_date?: string | null;
					notes?: string | null;
					is_active?: boolean | null;
					created_at?: string | null;
					updated_at?: string | null;
				};
				Update: {
					id?: string;
					employee_id?: string | null;
					service_id?: string | null;
					qualification_level?: string | null;
					certified_date?: string | null;
					expiry_date?: string | null;
					notes?: string | null;
					is_active?: boolean | null;
					created_at?: string | null;
					updated_at?: string | null;
				};
			};
			employee_time_off: {
				Row: {
					id: string;
					employee_id: string | null;
					start_date: string;
					end_date: string;
					start_time: string | null;
					end_time: string | null;
					reason: string | null;
					type: string | null;
					status: string | null;
					created_at: string | null;
					updated_at: string | null;
				};
				Insert: {
					id?: string;
					employee_id?: string | null;
					start_date: string;
					end_date: string;
					start_time?: string | null;
					end_time?: string | null;
					reason?: string | null;
					type?: string | null;
					status?: string | null;
					created_at?: string | null;
					updated_at?: string | null;
				};
				Update: {
					id?: string;
					employee_id?: string | null;
					start_date?: string;
					end_date?: string;
					start_time?: string | null;
					end_time?: string | null;
					reason?: string | null;
					type?: string | null;
					status?: string | null;
					created_at?: string | null;
					updated_at?: string | null;
				};
			};
			employees: {
				Row: {
					id: string;
					first_name: string;
					last_name: string;
					email: string;
					phone: string | null;
					role: string;
					employee_code: string | null;
					hire_date: string;
					hourly_rate: number | null;
					is_active: boolean | null;
					skills: string[] | null;
					languages: string[] | null;
					is_active: boolean;
					notes: string | null;
					created_at: string | null;
					updated_at: string | null;
					default_hourly_rate: number | null;
					is_available_for_scheduling: boolean;
					max_concurrent_services: number;
					scheduling_priority: number;
					created_at: string;
					updated_at: string;
					is_available_for_scheduling: boolean | null;
					max_concurrent_services: number | null;
					scheduling_priority: number | null;
					first_name: string | null;
					last_name: string | null;
					email: string | null;
					phone: string | null;
					role: string | null;
				};
				Insert: {
					id?: string;
					first_name: string;
					last_name: string;
					email: string;
					phone?: string | null;
					role: string;
					id: string;
					employee_code?: string | null;
					hire_date: string;
					hourly_rate?: number | null;
					is_active?: boolean | null;
					skills?: string[] | null;
					languages?: string[] | null;
					is_active?: boolean;
					notes?: string | null;
					created_at?: string | null;
					updated_at?: string | null;
					default_hourly_rate?: number | null;
					is_available_for_scheduling?: boolean;
					max_concurrent_services?: number;
					scheduling_priority?: number;
					created_at?: string;
					updated_at?: string;
					is_available_for_scheduling?: boolean | null;
					max_concurrent_services?: number | null;
					scheduling_priority?: number | null;
					first_name?: string | null;
					last_name?: string | null;
					email?: string | null;
					phone?: string | null;
					role?: string | null;
				};
				Update: {
					id?: string;
					first_name?: string;
					last_name?: string;
					email?: string;
					phone?: string | null;
					role?: string;
					employee_code?: string | null;
					hire_date?: string;
					hourly_rate?: number | null;
					is_active?: boolean | null;
					skills?: string[] | null;
					languages?: string[] | null;
					is_active?: boolean;
					notes?: string | null;
					created_at?: string | null;
					updated_at?: string | null;
					default_hourly_rate?: number | null;
					is_available_for_scheduling?: boolean;
					max_concurrent_services?: number;
					scheduling_priority?: number;
					created_at?: string;
					updated_at?: string;
					is_available_for_scheduling?: boolean | null;
					max_concurrent_services?: number | null;
					scheduling_priority?: number | null;
					first_name?: string | null;
					last_name?: string | null;
					email?: string | null;
					phone?: string | null;
					role?: string | null;
				};
			};
			customers: {
			equipment_reservations: {
				Row: {
					id: string;
					email: string;
					first_name: string;
					last_name: string;
					phone: string | null;
					emergency_contact_name: string | null;
					emergency_contact_phone: string | null;
					created_at: string;
					updated_at: string;
					time_slot_id: string;
					equipment_id: string;
					reserved_capacity: number;
					status: string;
					created_at: string | null;
					updated_at: string | null;
				};
				Insert: {
					id?: string;
					email: string;
					first_name: string;
					last_name: string;
					phone?: string | null;
					emergency_contact_name?: string | null;
					emergency_contact_phone?: string | null;
					created_at?: string;
					updated_at?: string;
					time_slot_id: string;
					equipment_id: string;
					reserved_capacity?: number;
					status?: string;
					created_at?: string | null;
					updated_at?: string | null;
				};
				Update: {
					id?: string;
					email?: string;
					first_name?: string;
					last_name?: string;
					phone?: string | null;
					emergency_contact_name?: string | null;
					emergency_contact_phone?: string | null;
					created_at?: string;
					updated_at?: string;
					time_slot_id?: string;
					equipment_id?: string;
					reserved_capacity?: number;
					status?: string;
					created_at?: string | null;
					updated_at?: string | null;
				};
			};
			notifications: {
				Row: {
					id: string;
					recipient_id: string;
					reservation_id: string | null;
					notification_type: string;
					subject: string;
					content: string;
					status: string;
					sent_at: string | null;
					error_message: string | null;
					created_at: string | null;
				};
				Insert: {
					id?: string;
					recipient_id: string;
					reservation_id?: string | null;
					notification_type: string;
					subject: string;
					content: string;
					status?: string;
					sent_at?: string | null;
					error_message?: string | null;
					created_at?: string | null;
				};
				Update: {
					id?: string;
					recipient_id?: string;
					reservation_id?: string | null;
					notification_type?: string;
					subject?: string;
					content?: string;
					status?: string;
					sent_at?: string | null;
					error_message?: string | null;
					created_at?: string | null;
				};
			};
			payments: {
				Row: {
					id: string;
					reservation_id: string;
					payment_method: string;
					payment_intent_id: string | null;
					amount: number;
					currency: string;
					status: string;
					payment_date: string | null;
					failure_reason: string | null;
					created_at: string | null;
					updated_at: string | null;
					payment_type: 'full' | 'deposit' | 'remaining' | null;
					deposit_percentage: number | null;
					is_deposit: boolean | null;
				};
				Insert: {
					id?: string;
					reservation_id: string;
					payment_method: string;
					payment_intent_id?: string | null;
					amount: number;
					currency?: string;
					status?: string;
					payment_date?: string | null;
					failure_reason?: string | null;
					created_at?: string | null;
					updated_at?: string | null;
				};
				Update: {
					id?: string;
					reservation_id?: string;
					payment_method?: string;
					payment_intent_id?: string | null;
					amount?: number;
					currency?: string;
					status?: string;
					payment_date?: string | null;
					failure_reason?: string | null;
					created_at?: string | null;
					updated_at?: string | null;
				};
			};
			equipment: {
				Row: {
					id: string;
					name: string;
					description: string | null;
					total_capacity: number;
					capacity_per_participant: number;
					is_active: boolean;
					created_at: string;
					updated_at: string;
				};
				Insert: {
					id?: string;
					name: string;
					description?: string | null;
					total_capacity?: number;
					capacity_per_participant?: number;
					is_active?: boolean;
					created_at?: string;
					updated_at?: string;
				};
				Update: {
					id?: string;
					name?: string;
					description?: string | null;
					total_capacity?: number;
					capacity_per_participant?: number;
					is_active?: boolean;
					created_at?: string;
					updated_at?: string;
				};
			};
			pricing_tiers: {
				Row: {
					id: string;
					service_id: string;
					tier_name: string;
					min_age: number;
					max_age: number | null;
					price: number;
					is_active: boolean;
					created_at: string;
					updated_at: string;
				};
				Insert: {
					id?: string;
					service_id: string;
					tier_name: string;
					min_age?: number;
					max_age?: number | null;
					price: number;
					is_active?: boolean;
					created_at?: string;
					updated_at?: string;
				};
				Update: {
					id?: string;
					service_id?: string;
					tier_name?: string;
					min_age?: number;
					max_age?: number | null;
					price?: number;
					is_active?: boolean;
					created_at?: string;
					updated_at?: string;
				};
			};
			employee_availability: {
				Row: {
					id: string;
					employee_id: string;
					day_of_week: number;
					start_time: string;
					end_time: string;
					is_available: boolean;
					effective_from: string | null;
					effective_until: string | null;
					created_at: string;
					updated_at: string;
				};
				Insert: {
					id?: string;
					employee_id: string;
					day_of_week: number;
					start_time: string;
					end_time: string;
					is_available?: boolean;
					effective_from?: string | null;
					effective_until?: string | null;
					created_at?: string;
					updated_at?: string;
				};
				Update: {
					id?: string;
					employee_id?: string;
					day_of_week?: number;
					start_time?: string;
					end_time?: string;
					is_available?: boolean;
					effective_from?: string | null;
					effective_until?: string | null;
					created_at?: string;
					updated_at?: string;
				};
			};
			employee_time_off: {
				Row: {
					id: string;
					employee_id: string;
					start_date: string;
					end_date: string;
					start_time: string | null;
					end_time: string | null;
					reason: string | null;
					type: string;
					status: string;
					created_at: string;
					updated_at: string;
				};
				Insert: {
					id?: string;
					employee_id: string;
					start_date: string;
					end_date: string;
					start_time?: string | null;
					end_time?: string | null;
					reason?: string | null;
					type?: string;
					status?: string;
					created_at?: string;
					updated_at?: string;
				};
				Update: {
					id?: string;
					employee_id?: string;
					start_date?: string;
					end_date?: string;
					start_time?: string | null;
					end_time?: string | null;
					reason?: string | null;
					type?: string;
					status?: string;
					created_at?: string;
					updated_at?: string;
				};
			};

			reservations: {
				Row: {
					id: string;
					customer_id: string;
					service_id: string;
					assigned_employee_id: string | null;
					start_time: string;
					end_time: string;
					reservation_number: string;
					participant_count: number;
					total_amount: number;
					currency: string;
					status: "pending" | "confirmed" | "cancelled" | "completed" | "no_show";
					special_requests: string | null;
					discount_code: string | null;
					discount_amount: number;
					check_in_time: string | null;
					qr_code: string | null;
					booking_source: string;
					admin_notes: string | null;
					requires_confirmation: boolean;
					confirmed_at: string | null;
					confirmed_by: string | null;
					created_at: string;
					updated_at: string;
					deposit_amount: number | null;
					remaining_amount: number | null;
					deposit_paid: boolean | null;
					deposit_payment_id: string | null;
				};
				Insert: {
					id?: string;
					customer_id: string;
					service_id: string;
					assigned_employee_id?: string | null;
					start_time: string;
					end_time: string;
					reservation_number: string;
					participant_count?: number;
					total_amount: number;
					currency?: string;
					status?: "pending" | "confirmed" | "cancelled" | "completed" | "no_show";
					special_requests?: string | null;
					discount_code?: string | null;
					discount_amount?: number;
					check_in_time?: string | null;
					qr_code?: string | null;
					booking_source?: string;
					admin_notes?: string | null;
					requires_confirmation?: boolean;
					confirmed_at?: string | null;
					confirmed_by?: string | null;
					created_at?: string;
					updated_at?: string;
				};
				Update: {
					id?: string;
					customer_id?: string;
					service_id?: string;
					assigned_employee_id?: string | null;
					start_time?: string;
					end_time?: string;
					reservation_number?: string;
					participant_count?: number;
					total_amount?: number;
					currency?: string;
					status?: "pending" | "confirmed" | "cancelled" | "completed" | "no_show";
					special_requests?: string | null;
					discount_code?: string | null;
					discount_amount?: number;
					check_in_time?: string | null;
					qr_code?: string | null;
					booking_source?: string;
					admin_notes?: string | null;
					requires_confirmation?: boolean;
					confirmed_at?: string | null;
					confirmed_by?: string | null;
					created_at?: string;
					updated_at?: string;
				};
			};
			service_scheduling_rules: {
				Row: {
					id: string;
					service_id: string;
					day_of_week: number | null;
					min_advance_booking_hours: number;
					max_advance_booking_days: number;
					operating_start_time: string | null;
					operating_end_time: string | null;
					booking_interval_minutes: number | null;
					specific_times: string[] | null;
					max_bookings_per_day: number | null;
					is_active: boolean;
					created_at: string;
					updated_at: string;
				};
				Insert: {
					id?: string;
					service_id: string;
					day_of_week?: number | null;
					min_advance_booking_hours?: number;
					max_advance_booking_days?: number;
					operating_start_time?: string | null;
					operating_end_time?: string | null;
					booking_interval_minutes?: number | null;
					specific_times?: string[] | null;
					max_bookings_per_day?: number | null;
					is_active?: boolean;
					created_at?: string;
					updated_at?: string;
				};
				Update: {
					id?: string;
					service_id?: string;
					day_of_week?: number | null;
					min_advance_booking_hours?: number;
					max_advance_booking_days?: number;
					operating_start_time?: string | null;
					operating_end_time?: string | null;
					booking_interval_minutes?: number | null;
					specific_times?: string[] | null;
					max_bookings_per_day?: number | null;
					is_active?: boolean;
					created_at?: string;
					updated_at?: string;
				};
			};
			service_blackout_dates: {
				Row: {
					id: string;
					service_id: string;
					start_date: string;
					end_date: string;
					reason: string | null;
					is_active: boolean;
					created_at: string;
					updated_at: string;
				};
				Insert: {
					id?: string;
					service_id: string;
					start_date: string;
					end_date: string;
					reason?: string | null;
					is_active?: boolean;
					created_at?: string;
					updated_at?: string;
				};
				Update: {
					id?: string;
					service_id?: string;
					start_date?: string;
					end_date?: string;
					reason?: string | null;
					is_active?: boolean;
					created_at?: string;
					updated_at?: string;
				};
			};
			employee_service_qualifications: {
				Row: {
					id: string;
					employee_id: string;
					service_id: string;
					qualification_level: string;
					certified_date: string | null;
					expiry_date: string | null;
					notes: string | null;
					is_active: boolean;
					created_at: string;
					updated_at: string;
				};
				Insert: {
					id?: string;
					employee_id: string;
					service_id: string;
					qualification_level?: string;
					certified_date?: string | null;
					expiry_date?: string | null;
					notes?: string | null;
					is_active?: boolean;
					created_at?: string;
					updated_at?: string;
				};
				Update: {
					id?: string;
					employee_id?: string;
					service_id?: string;
					qualification_level?: string;
					certified_date?: string | null;
					expiry_date?: string | null;
					notes?: string | null;
					is_active?: boolean;
					created_at?: string;
					updated_at?: string;
				};
			};
			schedule_templates: {
				Row: {
					id: string;
					name: string;
					description: string | null;
					service_id: string;
					template_data: any;
					is_active: boolean;
					created_at: string;
					updated_at: string;
				};
				Insert: {
					id?: string;
					name: string;
					description?: string | null;
					service_id: string;
					template_data: any;
					is_active?: boolean;
					created_at?: string;
					updated_at?: string;
				};
				Update: {
					id?: string;
					name?: string;
					description?: string | null;
					service_id?: string;
					template_data?: any;
					is_active?: boolean;
					created_at?: string;
					updated_at?: string;
				};
			};
			admin_audit_log: {
				Row: {
					id: string;
					admin_user_id: string | null;
					action: string;
					table_name: string | null;
					record_id: string | null;
					old_values: any | null;
					new_values: any | null;
					ip_address: string | null;
					user_agent: string | null;
					session_id: string | null;
					created_at: string;
				};
				Insert: {
					id?: string;
					admin_user_id?: string | null;
					action: string;
					table_name?: string | null;
					record_id?: string | null;
					old_values?: any | null;
					new_values?: any | null;
					ip_address?: string | null;
					user_agent?: string | null;
					session_id?: string | null;
					created_at?: string;
				};
				Update: {
					id?: string;
					admin_user_id?: string | null;
					action?: string;
					table_name?: string | null;
					record_id?: string | null;
					old_values?: any | null;
					new_values?: any | null;
					ip_address?: string | null;
					user_agent?: string | null;
					session_id?: string | null;
					created_at?: string;
				};
			};
			refunds: {
				Row: {
					id: string;
					payment_id: string;
					reservation_id: string;
					refund_amount: number;
					refund_reason: string;
					refund_method: string;
					status: string;
					processed_by: string | null;
					processed_at: string | null;
					external_refund_id: string | null;
					created_at: string | null;
					updated_at: string | null;
				};
				Insert: {
					id?: string;
					payment_id: string;
					reservation_id: string;
					refund_amount: number;
					refund_reason: string;
					refund_method: string;
					status?: string;
					processed_by?: string | null;
					processed_at?: string | null;
					external_refund_id?: string | null;
					created_at?: string | null;
					updated_at?: string | null;
				};
				Update: {
					id?: string;
					payment_id?: string;
					reservation_id?: string;
					refund_amount?: number;
					refund_reason?: string;
					refund_method?: string;
					status?: string;
					processed_by?: string | null;
					processed_at?: string | null;
					external_refund_id?: string | null;
					created_at?: string | null;
					updated_at?: string | null;
				};
			};
			service_blackout_dates: {
				Row: {
					id: string;
					service_id: string | null;
					start_date: string;
					end_date: string;
					reason: string | null;
					is_active: boolean | null;
					created_at: string | null;
					updated_at: string | null;
				};
				Insert: {
					id?: string;
					service_id?: string | null;
					start_date: string;
					end_date: string;
					reason?: string | null;
					is_active?: boolean | null;
					created_at?: string | null;
					updated_at?: string | null;
				};
				Update: {
					id?: string;
					service_id?: string | null;
					start_date?: string;
					end_date?: string;
					reason?: string | null;
					is_active?: boolean | null;
					created_at?: string | null;
					updated_at?: string | null;
				};
			};
			service_equipment_requirements: {
				Row: {
					id: string;
					service_id: string;
					equipment_id: string;
					capacity_per_participant: number;
				};
				Insert: {
					id?: string;
					service_id: string;
					equipment_id: string;
					capacity_per_participant?: number;
				};
				Update: {
					id?: string;
					service_id?: string;
					equipment_id?: string;
					capacity_per_participant?: number;
				};
			};
			service_scheduling_rules: {
				Row: {
					id: string;
					service_id: string | null;
					day_of_week: number | null;
					min_advance_booking_hours: number | null;
					max_advance_booking_days: number | null;
					operating_start_time: string | null;
					operating_end_time: string | null;
					booking_interval_minutes: number | null;
					specific_times: string[] | null;
					max_bookings_per_day: number | null;
					is_active: boolean | null;
					created_at: string | null;
					updated_at: string | null;
				};
				Insert: {
					id?: string;
					service_id?: string | null;
					day_of_week?: number | null;
					min_advance_booking_hours?: number | null;
					max_advance_booking_days?: number | null;
					operating_start_time?: string | null;
					operating_end_time?: string | null;
					booking_interval_minutes?: number | null;
					specific_times?: string[] | null;
					max_bookings_per_day?: number | null;
					is_active?: boolean | null;
					created_at?: string | null;
					updated_at?: string | null;
				};
				Update: {
					id?: string;
					service_id?: string | null;
					day_of_week?: number | null;
					min_advance_booking_hours?: number | null;
					max_advance_booking_days?: number | null;
					operating_start_time?: string | null;
					operating_end_time?: string | null;
					booking_interval_minutes?: number | null;
					specific_times?: string[] | null;
					max_bookings_per_day?: number | null;
					is_active?: boolean | null;
					created_at?: string | null;
					updated_at?: string | null;
				};
			};
			customer_analytics: {
				Row: {
					id: string;
					customer_id: string;
					total_reservations: number | null;
					completed_reservations: number | null;
					cancelled_reservations: number | null;
					no_show_reservations: number | null;
					total_spent: number | null;
					total_participants: number | null;
					average_rating: number | null;
					total_reviews: number | null;
					first_reservation_date: string | null;
					last_reservation_date: string | null;
					favorite_service_id: string | null;
					preferred_time_slot: string | null;
					average_group_size: number | null;
					customer_lifetime_value: number | null;
					loyalty_tier: string | null;
					last_updated: string | null;
					created_at: string | null;
				};
				Insert: {
					id?: string;
					customer_id: string;
					total_reservations?: number | null;
					completed_reservations?: number | null;
					cancelled_reservations?: number | null;
					no_show_reservations?: number | null;
					total_spent?: number | null;
					total_participants?: number | null;
					average_rating?: number | null;
					total_reviews?: number | null;
					first_reservation_date?: string | null;
					last_reservation_date?: string | null;
					favorite_service_id?: string | null;
					preferred_time_slot?: string | null;
					average_group_size?: number | null;
					customer_lifetime_value?: number | null;
					loyalty_tier?: string | null;
					last_updated?: string | null;
					created_at?: string | null;
				};
				Update: {
					id?: string;
					customer_id?: string;
					total_reservations?: number | null;
					completed_reservations?: number | null;
					cancelled_reservations?: number | null;
					no_show_reservations?: number | null;
					total_spent?: number | null;
					total_participants?: number | null;
					average_rating?: number | null;
					total_reviews?: number | null;
					first_reservation_date?: string | null;
					last_reservation_date?: string | null;
					favorite_service_id?: string | null;
					preferred_time_slot?: string | null;
					average_group_size?: number | null;
					customer_lifetime_value?: number | null;
					loyalty_tier?: string | null;
					last_updated?: string | null;
					created_at?: string | null;
				};
			};
			reservation_status_history: {
				Row: {
					id: string;
					reservation_id: string;
					old_status: string | null;
					new_status: string;
					changed_by: string | null;
					change_reason: string | null;
					automated_change: boolean | null;
					metadata: Json | null;
					created_at: string | null;
				};
				Insert: {
					id?: string;
					reservation_id: string;
					old_status?: string | null;
					new_status: string;
					changed_by?: string | null;
					change_reason?: string | null;
					automated_change?: boolean | null;
					metadata?: Json | null;
					created_at?: string | null;
				};
				Update: {
					id?: string;
					reservation_id?: string;
					old_status?: string | null;
					new_status?: string;
					changed_by?: string | null;
					change_reason?: string | null;
					automated_change?: boolean | null;
					metadata?: Json | null;
					created_at?: string | null;
				};
			};
			service_analytics: {
				Row: {
					id: string;
					service_id: string;
					period_start: string;
					period_end: string;
					total_bookings: number | null;
					confirmed_bookings: number | null;
					cancelled_bookings: number | null;
					no_show_bookings: number | null;
					total_revenue: number | null;
					total_participants: number | null;
					average_group_size: number | null;
					occupancy_rate: number | null;
					average_rating: number | null;
					total_reviews: number | null;
					repeat_customer_rate: number | null;
					cancellation_rate: number | null;
					no_show_rate: number | null;
					peak_booking_hour: number | null;
					peak_booking_day: number | null;
					created_at: string | null;
					updated_at: string | null;
				};
				Insert: {
					id?: string;
					service_id: string;
					period_start: string;
					period_end: string;
					total_bookings?: number | null;
					confirmed_bookings?: number | null;
					cancelled_bookings?: number | null;
					no_show_bookings?: number | null;
					total_revenue?: number | null;
					total_participants?: number | null;
					average_group_size?: number | null;
					occupancy_rate?: number | null;
					average_rating?: number | null;
					total_reviews?: number | null;
					repeat_customer_rate?: number | null;
					cancellation_rate?: number | null;
					no_show_rate?: number | null;
					peak_booking_hour?: number | null;
					peak_booking_day?: number | null;
					created_at?: string | null;
					updated_at?: string | null;
				};
				Update: {
					id?: string;
					service_id?: string;
					period_start?: string;
					period_end?: string;
					total_bookings?: number | null;
					confirmed_bookings?: number | null;
					cancelled_bookings?: number | null;
					no_show_bookings?: number | null;
					total_revenue?: number | null;
					total_participants?: number | null;
					average_group_size?: number | null;
					occupancy_rate?: number | null;
					average_rating?: number | null;
					total_reviews?: number | null;
					repeat_customer_rate?: number | null;
					cancellation_rate?: number | null;
					no_show_rate?: number | null;
					peak_booking_hour?: number | null;
					peak_booking_day?: number | null;
					created_at?: string | null;
					updated_at?: string | null;
				};
			};
			employee_analytics: {
				Row: {
					id: string;
					employee_id: string;
					period_start: string;
					period_end: string;
					total_assignments: number | null;
					completed_assignments: number | null;
					total_revenue_generated: number | null;
					average_customer_rating: number | null;
					created_at: string | null;
				};
				Insert: {
					id?: string;
					employee_id: string;
					period_start: string;
					period_end: string;
					total_assignments?: number | null;
					completed_assignments?: number | null;
					total_revenue_generated?: number | null;
					average_customer_rating?: number | null;
					created_at?: string | null;
				};
				Update: {
					id?: string;
					employee_id?: string;
					period_start?: string;
					period_end?: string;
					total_assignments?: number | null;
					completed_assignments?: number | null;
					total_revenue_generated?: number | null;
					average_customer_rating?: number | null;
					created_at?: string | null;
				};
			};
			daily_business_metrics: {
				Row: {
					id: string;
					metric_date: string;
					total_reservations: number | null;
					confirmed_reservations: number | null;
					cancelled_reservations: number | null;
					total_revenue: number | null;
					total_participants: number | null;
					new_customers: number | null;
					repeat_customers: number | null;
					average_order_value: number | null;
					occupancy_rate: number | null;
					customer_satisfaction: number | null;
					weather_condition: string | null;
					special_events: string[] | null;
					created_at: string | null;
					updated_at: string | null;
				};
				Insert: {
					id?: string;
					metric_date: string;
					total_reservations?: number | null;
					confirmed_reservations?: number | null;
					cancelled_reservations?: number | null;
					total_revenue?: number | null;
					total_participants?: number | null;
					new_customers?: number | null;
					repeat_customers?: number | null;
					average_order_value?: number | null;
					occupancy_rate?: number | null;
					customer_satisfaction?: number | null;
					weather_condition?: string | null;
					special_events?: string[] | null;
					created_at?: string | null;
					updated_at?: string | null;
				};
				Update: {
					id?: string;
					metric_date?: string;
					total_reservations?: number | null;
					confirmed_reservations?: number | null;
					cancelled_reservations?: number | null;
					total_revenue?: number | null;
					total_participants?: number | null;
					new_customers?: number | null;
					repeat_customers?: number | null;
					average_order_value?: number | null;
					occupancy_rate?: number | null;
					customer_satisfaction?: number | null;
					weather_condition?: string | null;
					special_events?: string[] | null;
					created_at?: string | null;
					updated_at?: string | null;
				};
			};
			equipment_utilization_history: {
				Row: {
					id: string;
					equipment_id: string;
					usage_date: string;
					total_capacity_hours: number | null;
					utilized_capacity_hours: number | null;
					utilization_rate: number | null;
					maintenance_hours: number | null;
					downtime_hours: number | null;
					revenue_generated: number | null;
					bookings_count: number | null;
					created_at: string | null;
				};
				Insert: {
					id?: string;
					equipment_id: string;
					usage_date: string;
					total_capacity_hours?: number | null;
					utilized_capacity_hours?: number | null;
					utilization_rate?: number | null;
					maintenance_hours?: number | null;
					downtime_hours?: number | null;
					revenue_generated?: number | null;
					bookings_count?: number | null;
					created_at?: string | null;
				};
				Update: {
					id?: string;
					equipment_id?: string;
					usage_date?: string;
					total_capacity_hours?: number | null;
					utilized_capacity_hours?: number | null;
					utilization_rate?: number | null;
					maintenance_hours?: number | null;
					downtime_hours?: number | null;
					revenue_generated?: number | null;
					bookings_count?: number | null;
					created_at?: string | null;
				};
			};
			customer_journey_events: {
				Row: {
					id: string;
					customer_id: string;
					event_type: string;
					event_data: Json | null;
					reservation_id: string | null;
					service_id: string | null;
					session_id: string | null;
					user_agent: string | null;
					ip_address: string | null;
					referrer_url: string | null;
					created_at: string | null;
				};
				Insert: {
					id?: string;
					customer_id: string;
					event_type: string;
					event_data?: Json | null;
					reservation_id?: string | null;
					service_id?: string | null;
					session_id?: string | null;
					user_agent?: string | null;
					ip_address?: string | null;
					referrer_url?: string | null;
					created_at?: string | null;
				};
				Update: {
					id?: string;
					customer_id?: string;
					event_type?: string;
					event_data?: Json | null;
					reservation_id?: string | null;
					service_id?: string | null;
					session_id?: string | null;
					user_agent?: string | null;
					ip_address?: string | null;
					referrer_url?: string | null;
					created_at?: string | null;
				};
			};
		};
		Views: {
			[_ in never]: never;
			customer_summary: {
				Row: {
					id: string;
					email: string;
					first_name: string | null;
					last_name: string | null;
					customer_since: string | null;
					total_reservations: number | null;
					completed_reservations: number | null;
					cancelled_reservations: number | null;
					total_spent: number | null;
					total_participants: number | null;
					average_order_value: number | null;
					last_reservation_date: string | null;
					first_reservation_date: string | null;
					average_rating: number | null;
					total_reviews: number | null;
				};
			};
			service_performance: {
				Row: {
					id: string;
					name: string;
					category: string | null;
					base_price: number;
					max_participants: number;
					total_bookings: number | null;
					confirmed_bookings: number | null;
					completed_bookings: number | null;
					cancelled_bookings: number | null;
					total_revenue: number | null;
					total_participants: number | null;
					avg_group_size: number | null;
					average_rating: number | null;
					total_reviews: number | null;
					cancellation_rate: number | null;
				};
			};
		};
		Functions: {
			[_ in never]: never;
			update_customer_analytics: {
				Args: {
					customer_uuid: string;
				};
				Returns: undefined;
			};
		};
		Enums: {
			[_ in never]: never;
		};
	};
}
