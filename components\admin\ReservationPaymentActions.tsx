"use client";

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { <PERSON><PERSON>, DialogContent, <PERSON><PERSON>Header, <PERSON><PERSON>Title, DialogTrigger } from '@/components/ui/dialog';
import { CreditCard, Eye } from 'lucide-react';
import { PaymentManagement } from './PaymentManagement';
import { DepositStatusBadge } from './DepositStatusBadge';

interface ReservationPaymentActionsProps {
  reservation: {
    id: string;
    total_amount: number;
    deposit_amount?: number | null;
    remaining_amount?: number | null;
    deposit_paid?: boolean | null;
    status: string;
  };
  payments?: Array<{
    amount: number;
    status: string;
    payment_type: string | null;
    is_deposit: boolean | null;
    completed_manually?: boolean | null;
    payment_method?: string;
  }>;
  compact?: boolean;
}

export function ReservationPaymentActions({ 
  reservation, 
  payments = [], 
  compact = false 
}: ReservationPaymentActionsProps) {
  const [showPaymentDialog, setShowPaymentDialog] = useState(false);

  const successfulPayments = payments.filter(p => p.status === 'succeeded');
  const totalPaid = successfulPayments.reduce((sum, p) => sum + p.amount, 0);
  const remainingAmount = reservation.total_amount - totalPaid;
  const hasRemainingBalance = remainingAmount > 0;

  if (compact) {
    return (
      <div className="flex items-center gap-2">
        <DepositStatusBadge reservation={reservation} payments={payments} />
        {hasRemainingBalance && (
          <Dialog open={showPaymentDialog} onOpenChange={setShowPaymentDialog}>
            <DialogTrigger asChild>
              <Button variant="outline" size="sm">
                <CreditCard className="w-3 h-3 mr-1" />
                Gérer
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
              <DialogHeader>
                <DialogTitle>Gestion des paiements</DialogTitle>
              </DialogHeader>
              <PaymentManagement reservationId={reservation.id} />
            </DialogContent>
          </Dialog>
        )}
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold">Statut de paiement</h3>
        <DepositStatusBadge reservation={reservation} payments={payments} />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
        <div className="bg-gray-50 p-3 rounded-lg">
          <div className="text-gray-600">Montant total</div>
          <div className="font-semibold">{reservation.total_amount}€</div>
        </div>
        <div className="bg-emerald-50 p-3 rounded-lg">
          <div className="text-gray-600">Montant payé</div>
          <div className="font-semibold text-emerald-600">{totalPaid}€</div>
        </div>
        <div className="bg-orange-50 p-3 rounded-lg">
          <div className="text-gray-600">Solde restant</div>
          <div className="font-semibold text-orange-600">{remainingAmount}€</div>
        </div>
      </div>

      {successfulPayments.length > 0 && (
        <div>
          <h4 className="font-medium mb-2">Paiements reçus</h4>
          <div className="space-y-2">
            {successfulPayments.map((payment, index) => (
              <div key={index} className="flex justify-between items-center text-sm border-l-4 border-emerald-500 pl-3 py-1">
                <div>
                  <span className="font-medium">{payment.amount}€</span>
                  {payment.is_deposit && <span className="text-xs text-gray-500 ml-2">(Acompte)</span>}
                  {payment.completed_manually && <span className="text-xs text-blue-500 ml-2">(Manuel)</span>}
                </div>
                <div className="text-gray-500">
                  {payment.payment_method === 'card' ? 'Carte' : 
                   payment.payment_method === 'cash' ? 'Espèces' : 
                   payment.payment_method || 'Autre'}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      <div className="flex gap-2">
        <Dialog open={showPaymentDialog} onOpenChange={setShowPaymentDialog}>
          <DialogTrigger asChild>
            <Button variant="outline" className="flex items-center gap-2">
              <Eye className="w-4 h-4" />
              Voir détails
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>Gestion des paiements</DialogTitle>
            </DialogHeader>
            <PaymentManagement reservationId={reservation.id} />
          </DialogContent>
        </Dialog>

        {hasRemainingBalance && (
          <Dialog>
            <DialogTrigger asChild>
              <Button className="bg-emerald-500 hover:bg-emerald-600 flex items-center gap-2">
                <CreditCard className="w-4 h-4" />
                Marquer comme payé
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
              <DialogHeader>
                <DialogTitle>Marquer le paiement comme terminé</DialogTitle>
              </DialogHeader>
              <PaymentManagement reservationId={reservation.id} />
            </DialogContent>
          </Dialog>
        )}
      </div>
    </div>
  );
}
