import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';
import { confirmPaymentIntent } from '@/lib/stripe';

export async function GET(
  request: NextRequest,
  { params }: { params: { paymentIntentId: string } }
) {
  try {
    const { paymentIntentId } = params;

    if (!paymentIntentId) {
      return NextResponse.json(
        { error: 'Payment intent ID is required' },
        { status: 400 }
      );
    }

    // Get payment status from database
    const { data: payment, error: paymentError } = await supabase
      .from('payments')
      .select(`
        *,
        reservation:reservations(
          id,
          reservation_number,
          status,
          total_amount,
          service:services(name)
        )
      `)
      .eq('payment_intent_id', paymentIntentId)
      .single();

    if (paymentError || !payment) {
      return NextResponse.json(
        { error: 'Payment not found' },
        { status: 404 }
      );
    }

    // Also check with Strip<PERSON> for the latest status
    const stripeResult = await confirmPaymentIntent(paymentIntentId);
    
    let latestStatus = payment.status;
    if (stripeResult.success && stripeResult.paymentIntent) {
      latestStatus = stripeResult.paymentIntent.status;
      
      // Update database if status has changed
      if (latestStatus !== payment.status) {
        await supabase
          .from('payments')
          .update({
            status: latestStatus,
            updated_at: new Date().toISOString(),
          })
          .eq('payment_intent_id', paymentIntentId);
      }
    }

    return NextResponse.json({
      success: true,
      payment: {
        id: payment.id,
        paymentIntentId: payment.payment_intent_id,
        amount: payment.amount,
        currency: payment.currency,
        status: latestStatus,
        paymentDate: payment.payment_date,
        failureReason: payment.failure_reason,
        createdAt: payment.created_at,
        updatedAt: payment.updated_at,
      },
      reservation: payment.reservation,
    });

  } catch (error) {
    console.error('Error getting payment status:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
