export type Equipment = {
	Row: {
		id: string;
		name: string;
		description: string | null;
		total_capacity: number;
		capacity_per_participant: number;
		is_active: boolean;
		created_at: string | null;
		updated_at: string | null;
	};
	Insert: Partial<Equipment["Row"]> & {
		name: string;
		total_capacity?: number;
		capacity_per_participant?: number;
	};
	Update: Partial<Equipment["Row"]>;
};

export type EquipmentReservations = {
	Row: {
		id: string;
		time_slot_id: string;
		equipment_id: string;
		quantity_reserved: number;
		status: string;
		created_at: string | null;
		updated_at: string | null;
	};
	Insert: Partial<EquipmentReservations["Row"]> & {
		time_slot_id: string;
		equipment_id: string;
		quantity_reserved: number;
	};
	Update: Partial<EquipmentReservations["Row"]>;
};

export type EquipmentUtilizationHistory = {
	Row: {
		id: string;
		equipment_id: string;
		usage_date: string;
		total_capacity_hours: number;
		utilized_capacity_hours: number;
		utilization_rate: number;
		maintenance_hours: number;
		downtime_hours: number;
		revenue_generated: number;
		bookings_count: number;
		created_at: string | null;
	};
	Insert: Partial<EquipmentUtilizationHistory["Row"]> & {
		equipment_id: string;
		usage_date: string;
	};
	Update: Partial<EquipmentUtilizationHistory["Row"]>;
};
