import { loadStripe, Stripe } from "@stripe/stripe-js";
import StripeServer from "stripe";
import { paymentConfig, validateStripeConfig } from "./env";

// Client-side Stripe instance
let stripePromise: Promise<Stripe | null>;

export const getStripe = (): Promise<Stripe | null> => {
	if (!stripePromise) {
		const config = validateStripeConfig();
		if (!config.isValid) {
			console.error("Stripe configuration errors:", config.errors);
			return Promise.resolve(null);
		}

		stripePromise = loadStripe(paymentConfig.stripe.publishableKey!);
	}
	return stripePromise;
};

// Server-side Stripe instance
let stripeServer: StripeServer | null = null;

export const getStripeServer = (): StripeServer => {
	if (!stripeServer) {
		console.log("=== STRIPE SERVER INITIALIZATION ===");
		console.log("Checking Stripe configuration...");

		const config = validateStripeConfig();
		console.log("Stripe config validation:", config);

		if (!config.isValid) {
			console.error("Stripe configuration errors:", config.errors);
			throw new Error(`Stripe configuration errors: ${config.errors.join(", ")}`);
		}

		if (!paymentConfig.stripe.secretKey) {
			console.error("STRIPE_SECRET_KEY is missing");
			throw new Error("STRIPE_SECRET_KEY is required for server-side operations");
		}

		console.log("Creating Stripe server instance...");
		console.log("Secret key present:", !!paymentConfig.stripe.secretKey);
		console.log("Secret key starts with:", paymentConfig.stripe.secretKey?.substring(0, 7));

		stripeServer = new StripeServer(paymentConfig.stripe.secretKey, {
			apiVersion: "2024-06-20",
		});

		console.log("Stripe server instance created successfully");
	}

	return stripeServer;
};

// Payment intent creation helper
export interface CreatePaymentIntentParams {
	amount: number; // in cents
	currency?: string;
	reservationId: string;
	customerEmail?: string;
	customerName?: string;
	description?: string;
}

export const createPaymentIntent = async (params: CreatePaymentIntentParams) => {
	console.log("=== STRIPE PAYMENT INTENT CREATION ===");
	console.log("Params:", params);

	try {
		const stripe = getStripeServer();
		console.log("Stripe server instance created successfully");

		const { amount, currency = "eur", reservationId, customerEmail, customerName, description } = params;

		console.log("Creating payment intent with Stripe API...");
		const paymentIntent = await stripe.paymentIntents.create({
			amount,
			currency,
			metadata: {
				reservationId,
				customerEmail: customerEmail || "",
				customerName: customerName || "",
			},
			description: description || `Payment for reservation ${reservationId}`,
			automatic_payment_methods: {
				enabled: true,
			},
		});

		console.log("Payment intent created successfully:", {
			id: paymentIntent.id,
			amount: paymentIntent.amount,
			currency: paymentIntent.currency,
			status: paymentIntent.status,
		});

		return {
			success: true,
			paymentIntent,
			clientSecret: paymentIntent.client_secret,
		};
	} catch (error) {
		console.error("=== STRIPE ERROR ===");
		console.error("Error creating payment intent:", error);
		return {
			success: false,
			error: error instanceof Error ? error.message : "Unknown error",
		};
	}
};

// Payment confirmation helper
export const confirmPaymentIntent = async (paymentIntentId: string) => {
	const stripe = getStripeServer();

	try {
		const paymentIntent = await stripe.paymentIntents.retrieve(paymentIntentId);

		return {
			success: true,
			paymentIntent,
			status: paymentIntent.status,
		};
	} catch (error) {
		console.error("Error confirming payment intent:", error);
		return {
			success: false,
			error: error instanceof Error ? error.message : "Unknown error",
		};
	}
};

// Webhook signature verification
export const verifyWebhookSignature = (
	payload: string | Buffer,
	signature: string
): { success: boolean; event?: StripeServer.Event; error?: string } => {
	if (!paymentConfig.stripe.webhookSecret) {
		return {
			success: false,
			error: "Webhook secret not configured",
		};
	}

	try {
		const stripe = getStripeServer();
		const event = stripe.webhooks.constructEvent(payload, signature, paymentConfig.stripe.webhookSecret);

		return {
			success: true,
			event,
		};
	} catch (error) {
		console.error("Webhook signature verification failed:", error);
		return {
			success: false,
			error: error instanceof Error ? error.message : "Signature verification failed",
		};
	}
};

// Format amount for display (convert cents to euros)
export const formatAmount = (amountInCents: number, currency = "EUR"): string => {
	const amount = amountInCents / 100;
	return new Intl.NumberFormat("fr-FR", {
		style: "currency",
		currency,
	}).format(amount);
};

// Convert euros to cents for Stripe
export const eurosToCents = (euros: number): number => {
	return Math.round(euros * 100);
};

// Payment status helpers
export const isPaymentSuccessful = (status: string): boolean => {
	return status === "succeeded";
};

export const isPaymentPending = (status: string): boolean => {
	return ["processing", "requires_action", "requires_confirmation"].includes(status);
};

export const isPaymentFailed = (status: string): boolean => {
	return ["canceled", "payment_failed", "requires_payment_method"].includes(status);
};
