import { Json } from "./common";

export type Customers = {
	Row: {
		id: string;
		email: string;
		first_name: string;
		last_name: string;
		phone: string | null;
		date_of_birth: string | null;
		nationality: string | null;
		emergency_contact_name: string | null;
		emergency_contact_phone: string | null;
		dietary_restrictions: string | null;
		medical_conditions: string | null;
		marketing_consent: boolean | null;
		created_at: string | null;
		updated_at: string | null;
	};
	Insert: Partial<Customers["Row"]> & {
		id: string;
		email: string;
		first_name: string;
		last_name: string;
	};
	Update: Partial<Customers["Row"]>;
};

export type CustomerFeedback = {
	Row: {
		id: string;
		reservation_id: string;
		customer_id: string;
		rating: number;
		review_text: string | null;
		service_quality_rating: number | null;
		staff_rating: number | null;
		equipment_rating: number | null;
		would_recommend: boolean | null;
		is_public: boolean | null;
		response_text: string | null;
		responded_by: string | null;
		responded_at: string | null;
		created_at: string | null;
		updated_at: string | null;
	};
	Insert: Partial<CustomerFeedback["Row"]> & {
		reservation_id: string;
		customer_id: string;
		rating: number;
	};
	Update: Partial<CustomerFeedback["Row"]>;
};

export type CustomerAnalytics = {
	Row: {
		id: string;
		customer_id: string;
		total_reservations: number;
		completed_reservations: number;
		cancelled_reservations: number;
		no_show_reservations: number;
		total_spent: number;
		total_participants: number;
		average_rating: number | null;
		total_reviews: number;
		first_reservation_date: string | null;
		last_reservation_date: string | null;
		favorite_service_id: string | null;
		preferred_time_slot: string | null;
		average_group_size: number | null;
		customer_lifetime_value: number;
		loyalty_tier: string;
		last_updated: string | null;
		created_at: string | null;
	};
	Insert: Partial<CustomerAnalytics["Row"]> & { customer_id: string };
	Update: Partial<CustomerAnalytics["Row"]>;
};

export type CustomerJourneyEvents = {
	Row: {
		id: string;
		customer_id: string;
		event_type: string;
		event_data: Json | null;
		reservation_id: string | null;
		service_id: string | null;
		session_id: string | null;
		user_agent: string | null;
		ip_address: string | null;
		referrer_url: string | null;
		created_at: string | null;
	};
	Insert: Partial<CustomerJourneyEvents["Row"]> & {
		customer_id: string;
		event_type: string;
	};
	Update: Partial<CustomerJourneyEvents["Row"]>;
};
